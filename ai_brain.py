import math
import config
from utils import distance, normalize_vector, vector_magnitude, rotate_vector, lerp

class AIBrain:
    def __init__(self):
        self.target_direction = (1, 0)  # 默认向右移动
        self.current_direction = (1, 0)
        
    def calculate_avoidance_direction(self, ball_pos, obstacles):
        """计算躲避方向"""
        if not obstacles:
            return self.target_direction
        
        # 计算所有障碍物的排斥力
        total_avoidance_force = [0, 0]
        
        for obstacle in obstacles:
            obs_pos = obstacle.get_position()
            
            # 计算从障碍物到球的向量
            avoidance_vector = (ball_pos[0] - obs_pos[0], ball_pos[1] - obs_pos[1])
            dist = distance(ball_pos, obs_pos)
            
            if dist > 0:
                # 归一化向量
                avoidance_vector = normalize_vector(avoidance_vector)
                
                # 根据距离计算力的强度（距离越近，力越强）
                force_strength = config.AVOIDANCE_FORCE * (config.DETECTION_RADIUS / max(dist, 1))
                
                # 根据障碍物大小调整力度
                size_factor = obstacle.radius / 50.0  # 归一化大小因子
                force_strength *= (1 + size_factor)
                
                total_avoidance_force[0] += avoidance_vector[0] * force_strength
                total_avoidance_force[1] += avoidance_vector[1] * force_strength
        
        # 结合前进力和躲避力
        forward_force = (config.FORWARD_FORCE, 0)  # 向右的前进力
        
        final_direction = (
            forward_force[0] + total_avoidance_force[0],
            forward_force[1] + total_avoidance_force[1]
        )
        
        # 归一化最终方向
        return normalize_vector(final_direction)
    
    def update_direction(self, ball_pos, obstacles):
        """更新移动方向"""
        # 计算理想的躲避方向
        ideal_direction = self.calculate_avoidance_direction(ball_pos, obstacles)
        
        # 平滑过渡到新方向
        self.current_direction = (
            lerp(self.current_direction[0], ideal_direction[0], config.SMOOTHING_FACTOR),
            lerp(self.current_direction[1], ideal_direction[1], config.SMOOTHING_FACTOR)
        )
        
        # 确保方向向量不为零
        if vector_magnitude(self.current_direction) == 0:
            self.current_direction = (1, 0)
        else:
            self.current_direction = normalize_vector(self.current_direction)
        
        return self.current_direction
    
    def get_sensor_data(self, ball_pos, obstacles):
        """获取传感器数据（用于调试和可视化）"""
        sensor_data = {
            'obstacles_detected': len(obstacles),
            'nearest_obstacle_distance': float('inf'),
            'danger_level': 0
        }
        
        if obstacles:
            distances = [distance(ball_pos, obs.get_position()) for obs in obstacles]
            sensor_data['nearest_obstacle_distance'] = min(distances)
            
            # 计算危险等级（0-1之间）
            min_dist = sensor_data['nearest_obstacle_distance']
            if min_dist < config.DETECTION_RADIUS:
                sensor_data['danger_level'] = 1 - (min_dist / config.DETECTION_RADIUS)
        
        return sensor_data
    
    def predict_collision_path(self, ball_pos, ball_velocity, obstacles, steps=10):
        """预测碰撞路径"""
        future_positions = []
        current_pos = ball_pos
        
        for step in range(steps):
            future_pos = (
                current_pos[0] + ball_velocity[0] * step,
                current_pos[1] + ball_velocity[1] * step
            )
            future_positions.append(future_pos)
            
            # 检查是否会碰撞
            for obstacle in obstacles:
                if distance(future_pos, obstacle.get_position()) < obstacle.radius + config.BALL_RADIUS:
                    return future_positions[:step+1], True  # 返回碰撞前的路径
        
        return future_positions, False  # 没有预测到碰撞
