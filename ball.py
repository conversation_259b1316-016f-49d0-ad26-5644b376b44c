import pygame
import math
import config
from ai_brain import AIBrain
from utils import distance, normalize_vector, clamp

class Ball:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.radius = config.BALL_RADIUS
        self.speed = config.BALL_SPEED
        self.ai_brain = AIBrain()
        
        # 移动相关
        self.velocity = [0, 0]
        self.direction = (1, 0)  # 初始向右移动
        
        # 状态信息
        self.collision_count = 0
        self.distance_traveled = 0
        self.last_position = (x, y)
        
        # 可视化相关
        self.trail = []  # 轨迹点
        self.max_trail_length = 50
        
    def update(self, obstacles_manager):
        """更新球的状态"""
        # 获取附近的障碍物
        nearby_obstacles = obstacles_manager.get_obstacles_in_range(
            (self.x, self.y), config.DETECTION_RADIUS
        )
        
        # 使用AI大脑计算新方向
        self.direction = self.ai_brain.update_direction((self.x, self.y), nearby_obstacles)
        
        # 更新速度
        self.velocity[0] = self.direction[0] * self.speed
        self.velocity[1] = self.direction[1] * self.speed
        
        # 更新位置
        new_x = self.x + self.velocity[0]
        new_y = self.y + self.velocity[1]
        
        # 边界检查和反弹
        if new_x - self.radius <= 0 or new_x + self.radius >= config.SCREEN_WIDTH:
            self.velocity[0] *= -1
            new_x = clamp(new_x, self.radius, config.SCREEN_WIDTH - self.radius)
            
        if new_y - self.radius <= 0 or new_y + self.radius >= config.SCREEN_HEIGHT:
            self.velocity[1] *= -1
            new_y = clamp(new_y, self.radius, config.SCREEN_HEIGHT - self.radius)
        
        # 检查碰撞
        if obstacles_manager.check_collision_with_ball((new_x, new_y), self.radius):
            self.collision_count += 1
            # 简单的碰撞响应：反向移动
            self.velocity[0] *= -0.5
            self.velocity[1] *= -0.5
            new_x = self.x + self.velocity[0]
            new_y = self.y + self.velocity[1]
        
        # 更新位置
        self.x = new_x
        self.y = new_y
        
        # 更新统计信息
        self.distance_traveled += distance((self.x, self.y), self.last_position)
        self.last_position = (self.x, self.y)
        
        # 更新轨迹
        self.trail.append((self.x, self.y))
        if len(self.trail) > self.max_trail_length:
            self.trail.pop(0)
    
    def draw(self, screen):
        """绘制球和相关可视化元素"""
        # 绘制轨迹
        if len(self.trail) > 1:
            for i in range(1, len(self.trail)):
                alpha = i / len(self.trail)  # 透明度渐变
                color = (
                    int(config.BALL_COLOR[0] * alpha),
                    int(config.BALL_COLOR[1] * alpha),
                    int(config.BALL_COLOR[2] * alpha)
                )
                pygame.draw.circle(screen, color, (int(self.trail[i][0]), int(self.trail[i][1])), 2)
        
        # 绘制检测范围（调试用）
        pygame.draw.circle(screen, (0, 255, 0, 50), (int(self.x), int(self.y)), 
                          config.DETECTION_RADIUS, 1)
        
        # 绘制球体
        pygame.draw.circle(screen, config.BALL_COLOR, (int(self.x), int(self.y)), self.radius)
        pygame.draw.circle(screen, config.BLACK, (int(self.x), int(self.y)), self.radius, 2)
        
        # 绘制方向指示器
        end_x = self.x + self.direction[0] * (self.radius + 15)
        end_y = self.y + self.direction[1] * (self.radius + 15)
        pygame.draw.line(screen, config.YELLOW, (self.x, self.y), (end_x, end_y), 3)
    
    def get_position(self):
        """获取球的位置"""
        return (self.x, self.y)
    
    def get_stats(self):
        """获取统计信息"""
        return {
            'position': (int(self.x), int(self.y)),
            'collision_count': self.collision_count,
            'distance_traveled': int(self.distance_traveled),
            'current_speed': math.sqrt(self.velocity[0]**2 + self.velocity[1]**2)
        }
    
    def reset_position(self, x, y):
        """重置球的位置"""
        self.x = x
        self.y = y
        self.velocity = [0, 0]
        self.trail.clear()
        self.collision_count = 0
        self.distance_traveled = 0
