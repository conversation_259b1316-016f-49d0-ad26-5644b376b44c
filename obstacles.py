import pygame
import random
import config
from utils import distance

class Obstacle:
    def __init__(self, x, y, size):
        self.x = x
        self.y = y
        self.size = size
        self.radius = size // 2
        
    def draw(self, screen):
        """绘制障碍物"""
        pygame.draw.circle(screen, config.OBSTACLE_COLOR, (int(self.x), int(self.y)), self.radius)
        # 绘制边框
        pygame.draw.circle(screen, config.BLACK, (int(self.x), int(self.y)), self.radius, 2)
    
    def get_position(self):
        """获取障碍物位置"""
        return (self.x, self.y)
    
    def collides_with_ball(self, ball_pos, ball_radius):
        """检查是否与球碰撞"""
        return distance(self.get_position(), ball_pos) < (self.radius + ball_radius)

class ObstacleManager:
    def __init__(self):
        self.obstacles = []
        
    def update(self, ball_pos):
        """更新障碍物系统"""
        # 随机生成新障碍物
        if (random.random() < config.OBSTACLE_SPAWN_RATE and 
            len(self.obstacles) < config.MAX_OBSTACLES):
            self.spawn_obstacle(ball_pos)
        
        # 移除屏幕外的障碍物
        self.obstacles = [obs for obs in self.obstacles 
                         if obs.x > -obs.radius and obs.x < config.SCREEN_WIDTH + obs.radius
                         and obs.y > -obs.radius and obs.y < config.SCREEN_HEIGHT + obs.radius]
    
    def spawn_obstacle(self, ball_pos):
        """生成新障碍物"""
        size = random.randint(config.OBSTACLE_MIN_SIZE, config.OBSTACLE_MAX_SIZE)
        
        # 在屏幕边缘随机生成
        side = random.randint(0, 3)  # 0:上, 1:右, 2:下, 3:左
        
        if side == 0:  # 上边
            x = random.randint(0, config.SCREEN_WIDTH)
            y = -size
        elif side == 1:  # 右边
            x = config.SCREEN_WIDTH + size
            y = random.randint(0, config.SCREEN_HEIGHT)
        elif side == 2:  # 下边
            x = random.randint(0, config.SCREEN_WIDTH)
            y = config.SCREEN_HEIGHT + size
        else:  # 左边
            x = -size
            y = random.randint(0, config.SCREEN_HEIGHT)
        
        # 确保新障碍物不会太靠近球
        if distance((x, y), ball_pos) > 150:
            self.obstacles.append(Obstacle(x, y, size))
    
    def draw(self, screen):
        """绘制所有障碍物"""
        for obstacle in self.obstacles:
            obstacle.draw(screen)
    
    def get_obstacles_in_range(self, center_pos, detection_radius):
        """获取指定范围内的障碍物"""
        nearby_obstacles = []
        for obstacle in self.obstacles:
            if distance(obstacle.get_position(), center_pos) <= detection_radius:
                nearby_obstacles.append(obstacle)
        return nearby_obstacles
    
    def check_collision_with_ball(self, ball_pos, ball_radius):
        """检查球是否与任何障碍物碰撞"""
        for obstacle in self.obstacles:
            if obstacle.collides_with_ball(ball_pos, ball_radius):
                return True
        return False
