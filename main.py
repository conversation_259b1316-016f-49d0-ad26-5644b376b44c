import pygame
import sys
import config
from ball import Ball
from obstacles import ObstacleManager

class ObstacleAvoidanceGame:
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((config.SCREEN_WIDTH, config.SCREEN_HEIGHT))
        pygame.display.set_caption("AI障碍物躲避系统")
        self.clock = pygame.time.Clock()
        
        # 游戏对象
        self.ball = Ball(100, config.SCREEN_HEIGHT // 2)
        self.obstacle_manager = ObstacleManager()
        
        # 游戏状态
        self.running = True
        self.paused = False
        self.show_debug = True
        
        # 字体
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
        # 统计信息
        self.frame_count = 0
        
    def handle_events(self):
        """处理事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                elif event.key == pygame.K_r:
                    self.reset_game()
                elif event.key == pygame.K_d:
                    self.show_debug = not self.show_debug
                elif event.key == pygame.K_ESCAPE:
                    self.running = False
    
    def update(self):
        """更新游戏状态"""
        if not self.paused:
            self.ball.update(self.obstacle_manager)
            self.obstacle_manager.update(self.ball.get_position())
            self.frame_count += 1
    
    def draw_ui(self):
        """绘制用户界面"""
        # 获取球的统计信息
        ball_stats = self.ball.get_stats()
        
        # 获取AI传感器数据
        nearby_obstacles = self.obstacle_manager.get_obstacles_in_range(
            self.ball.get_position(), config.DETECTION_RADIUS
        )
        sensor_data = self.ball.ai_brain.get_sensor_data(
            self.ball.get_position(), nearby_obstacles
        )
        
        # 绘制统计信息
        y_offset = 10
        stats_texts = [
            f"位置: ({ball_stats['position'][0]}, {ball_stats['position'][1]})",
            f"碰撞次数: {ball_stats['collision_count']}",
            f"行进距离: {ball_stats['distance_traveled']}",
            f"当前速度: {ball_stats['current_speed']:.1f}",
            f"检测到障碍物: {sensor_data['obstacles_detected']}",
            f"最近障碍物距离: {sensor_data['nearest_obstacle_distance']:.1f}",
            f"危险等级: {sensor_data['danger_level']:.2f}",
            f"总障碍物数: {len(self.obstacle_manager.obstacles)}",
            f"帧数: {self.frame_count}"
        ]
        
        for text in stats_texts:
            surface = self.small_font.render(text, True, config.BLACK)
            self.screen.blit(surface, (10, y_offset))
            y_offset += 25
        
        # 绘制控制说明
        controls = [
            "控制说明:",
            "空格键 - 暂停/继续",
            "R键 - 重置游戏",
            "D键 - 切换调试信息",
            "ESC键 - 退出"
        ]
        
        y_offset = config.SCREEN_HEIGHT - len(controls) * 25 - 10
        for text in controls:
            surface = self.small_font.render(text, True, config.BLACK)
            self.screen.blit(surface, (10, y_offset))
            y_offset += 25
        
        # 绘制暂停提示
        if self.paused:
            pause_text = self.font.render("游戏已暂停 - 按空格键继续", True, config.RED)
            text_rect = pause_text.get_rect(center=(config.SCREEN_WIDTH // 2, 50))
            self.screen.blit(pause_text, text_rect)
    
    def draw(self):
        """绘制游戏画面"""
        self.screen.fill(config.WHITE)
        
        # 绘制游戏对象
        self.obstacle_manager.draw(self.screen)
        self.ball.draw(self.screen)
        
        # 绘制UI
        if self.show_debug:
            self.draw_ui()
        
        pygame.display.flip()
    
    def reset_game(self):
        """重置游戏"""
        self.ball.reset_position(100, config.SCREEN_HEIGHT // 2)
        self.obstacle_manager.obstacles.clear()
        self.frame_count = 0
        self.paused = False
    
    def run(self):
        """主游戏循环"""
        print("AI障碍物躲避系统启动!")
        print("控制说明:")
        print("- 空格键: 暂停/继续")
        print("- R键: 重置游戏")
        print("- D键: 切换调试信息")
        print("- ESC键: 退出")
        
        while self.running:
            self.handle_events()
            self.update()
            self.draw()
            self.clock.tick(config.FPS)
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = ObstacleAvoidanceGame()
    game.run()
