# AI障碍物躲避系统

一个基于人工智能的实时障碍物躲避系统，使用Python和Pygame实现。

## 功能特点

- **智能躲避**: 球体使用AI算法自动检测和躲避障碍物
- **实时生成**: 障碍物随机生成，创造动态挑战环境
- **可视化界面**: 直观的2D图形界面，显示球体轨迹和检测范围
- **性能统计**: 实时显示碰撞次数、行进距离等统计信息
- **调试模式**: 可视化AI的感知范围和决策过程

## AI算法原理

### 感知系统
- 检测半径内的所有障碍物
- 计算到每个障碍物的距离和方向
- 评估危险等级

### 决策系统
- 计算障碍物的排斥力
- 结合前进动力和躲避力
- 使用力场算法确定最佳移动方向

### 执行系统
- 平滑过渡到新方向，避免急转弯
- 边界检测和碰撞响应
- 轨迹记录和可视化

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python main.py
```

## 控制说明

- **空格键**: 暂停/继续游戏
- **R键**: 重置游戏状态
- **D键**: 切换调试信息显示
- **ESC键**: 退出程序

## 配置参数

可以在 `config.py` 文件中调整以下参数：

### 球体参数
- `BALL_RADIUS`: 球体半径
- `BALL_SPEED`: 球体移动速度
- `BALL_COLOR`: 球体颜色

### 障碍物参数
- `OBSTACLE_MIN_SIZE/MAX_SIZE`: 障碍物大小范围
- `OBSTACLE_SPAWN_RATE`: 障碍物生成频率
- `MAX_OBSTACLES`: 最大障碍物数量

### AI参数
- `DETECTION_RADIUS`: 障碍物检测半径
- `AVOIDANCE_FORCE`: 躲避力度
- `SMOOTHING_FACTOR`: 方向平滑因子

## 文件结构

```
ObstacleAvoidance/
├── main.py           # 主程序入口
├── ball.py           # 球体类
├── obstacles.py      # 障碍物系统
├── ai_brain.py       # AI决策算法
├── utils.py          # 工具函数
├── config.py         # 配置参数
├── requirements.txt  # 依赖包
└── README.md         # 说明文档
```

## 扩展建议

1. **强化学习**: 可以集成Q-learning或Deep Q-Network
2. **多智能体**: 添加多个球体的协作躲避
3. **3D环境**: 扩展到三维空间
4. **神经网络**: 使用深度学习优化决策算法
5. **物理引擎**: 集成更真实的物理模拟

## 技术栈

- **Python 3.7+**
- **Pygame 2.5+**: 图形界面和游戏循环
- **NumPy**: 数学计算
- **数学算法**: 向量计算、力场算法、路径规划
