import math
import numpy as np

def distance(pos1, pos2):
    """计算两点之间的距离"""
    return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

def normalize_vector(vector):
    """向量归一化"""
    magnitude = math.sqrt(vector[0]**2 + vector[1]**2)
    if magnitude == 0:
        return (0, 0)
    return (vector[0] / magnitude, vector[1] / magnitude)

def vector_magnitude(vector):
    """计算向量长度"""
    return math.sqrt(vector[0]**2 + vector[1]**2)

def dot_product(v1, v2):
    """计算两个向量的点积"""
    return v1[0] * v2[0] + v1[1] * v2[1]

def angle_between_vectors(v1, v2):
    """计算两个向量之间的角度"""
    dot = dot_product(normalize_vector(v1), normalize_vector(v2))
    # 防止数值误差导致的域错误
    dot = max(-1, min(1, dot))
    return math.acos(dot)

def rotate_vector(vector, angle):
    """旋转向量"""
    cos_a = math.cos(angle)
    sin_a = math.sin(angle)
    return (
        vector[0] * cos_a - vector[1] * sin_a,
        vector[0] * sin_a + vector[1] * cos_a
    )

def clamp(value, min_val, max_val):
    """限制数值在指定范围内"""
    return max(min_val, min(max_val, value))

def lerp(a, b, t):
    """线性插值"""
    return a + (b - a) * t
